mini BPE

Tokenizer 将文本转换为token

emmbedding 将离散的token ID转换为连续的向量表示

> 为什么建议在大模型中使用英文？
>
> 因为训练的语料库主要是英文，token占用少，而模型的上下文窗口是有限的，占用token越少效率越高

Unicode 

为什么不直接使用unicode作为编码？

词汇表太长而且不稳定

utf-8:将词汇拆解转换为字节，可能一个单词或者字会拆成多个部分，且最长只有256，这样也会导致token太长，浪费上下文窗口

### BPE(byte pair encoding)

原始的BPE算法非常简单，即**迭代地**统计数据中出现频次最多的字节对，并将其加入到词汇表中，直到不再有重复的字节对出现

![image-20250810100046540](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250810100046540.png)
